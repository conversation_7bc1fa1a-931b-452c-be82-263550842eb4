{% extends "be/include/preline-base.html" %}

{% block extrahead %}
<title>Gestione Permessi Utenti</title>
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
    addRoute('BE_USER_PERMISSIONS_MANAGER_USERS', '{{ routes("BE_USER_PERMISSIONS_MANAGER_USERS") }}');
    addRoute('BE_USER_PERMISSIONS', '{{ routes("BE_USER_PERMISSIONS") }}');
    addRoute('BE_USER_PERMISSIONS_SAVE', '{{ routes("BE_USER_PERMISSIONS_SAVE") }}');
</script>

<div class="lg:ps-65 p-2 sm:p-5 md:pt-5 space-y-5">
    <!-- Header Card -->
    <div class="bg-white border border-gray-200 shadow-xs rounded-xl dark:bg-neutral-900 dark:border-neutral-700">
        <div class="py-3 px-5 border-b border-gray-200 dark:border-neutral-700">
            <div class="flex flex-wrap justify-between items-center gap-2">
                <div class="flex flex-wrap items-center gap-2">
                    <h2 class="font-medium text-gray-800 dark:text-neutral-200">
                        Gestione Permessi Utenti
                    </h2>
                </div>
            </div>
        </div>
        
        <div class="p-5">
            <p class="text-sm text-gray-600 dark:text-neutral-400 mb-4">
                Seleziona un utente per visualizzare e modificare i suoi permessi. Puoi abilitare o disabilitare specifici tipi di permesso per ogni area funzionale.
            </p>
            
            <!-- User Selection -->
            <div class="mb-6">
                <label for="user-select" class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                    Seleziona Utente <span class="text-red-500">*</span>
                </label>
                <select id="user-select" class="py-2 px-3 pe-9 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:ring-neutral-600" data-hs-select='{
                    "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span class=\"me-2\" data-icon></span><span class=\"text-gray-800 dark:text-neutral-200\" data-title></span></button>",
                    "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-neutral-600",
                    "dropdownClasses": "mt-2 max-h-72 pb-1 px-1 space-y-0.5 z-20 w-full bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                    "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800"
                }'>
                    <option value="">Seleziona un utente...</option>
                    <!-- Options will be loaded via JavaScript -->
                </select>
            </div>
        </div>
    </div>

    <!-- Permissions Management Card -->
    <div id="permissions-card" class="bg-white border border-gray-200 shadow-xs rounded-xl dark:bg-neutral-900 dark:border-neutral-700" style="display: none;">
        <div class="py-3 px-5 border-b border-gray-200 dark:border-neutral-700">
            <div class="flex flex-wrap justify-between items-center gap-2">
                <div class="flex flex-wrap items-center gap-2">
                    <h3 class="font-medium text-gray-800 dark:text-neutral-200">
                        Permessi per: <span id="selected-user-name" class="text-blue-600 dark:text-blue-400"></span>
                    </h3>
                </div>
                <div class="flex flex-wrap items-center gap-2">
                    <button type="button" id="save-all-permissions-btn" class="py-1.5 px-2.5 inline-flex items-center gap-x-1 text-xs font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                        <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                            <polyline points="17,21 17,13 7,13 7,21"></polyline>
                            <polyline points="7,3 7,8 15,8"></polyline>
                        </svg>
                        Salva Tutti i Permessi
                    </button>
                </div>
            </div>
        </div>
        
        <div class="p-5">
            <!-- Permissions Container -->
            <div id="permissions-container" class="space-y-4">
                <!-- Loading State -->
                <div id="permissions-loading" class="text-center py-8">
                    <div class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-gray-500 bg-white dark:bg-neutral-800 dark:text-neutral-400">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Caricamento permessi...
                    </div>
                </div>

                <!-- Permissions will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Permission Card Template -->
<template id="permission-card-template">
    <div class="permission-item bg-gray-50 dark:bg-neutral-800 rounded-lg p-4 border border-gray-200 dark:border-neutral-700">
        <div class="flex items-start justify-between mb-4">
            <div class="flex-1">
                <h4 class="permission-name font-medium text-gray-900 dark:text-white text-sm mb-1"></h4>
                <p class="permission-description text-xs text-gray-500 dark:text-neutral-400 mb-2"></p>
                <span class="permission-code text-xs font-mono bg-gray-200 dark:bg-neutral-700 px-2 py-1 rounded text-gray-600 dark:text-neutral-300"></span>
            </div>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
            <label class="flex items-center p-2 bg-white dark:bg-neutral-900 rounded border border-gray-200 dark:border-neutral-700 hover:bg-gray-50 dark:hover:bg-neutral-800 cursor-pointer">
                <input type="checkbox" class="permission-checkbox shrink-0 border-gray-200 rounded text-green-600 focus:ring-green-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:checked:bg-green-500 dark:checked:border-green-500 dark:focus:ring-offset-gray-800" data-permission-type="view">
                <div class="ms-2">
                    <div class="flex items-center gap-1">
                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span class="text-xs font-medium text-gray-700 dark:text-neutral-300">VIEW</span>
                    </div>
                    <span class="text-xs text-gray-500 dark:text-neutral-400">Visualizza</span>
                </div>
            </label>
            
            <label class="flex items-center p-2 bg-white dark:bg-neutral-900 rounded border border-gray-200 dark:border-neutral-700 hover:bg-gray-50 dark:hover:bg-neutral-800 cursor-pointer">
                <input type="checkbox" class="permission-checkbox shrink-0 border-gray-200 rounded text-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" data-permission-type="create">
                <div class="ms-2">
                    <div class="flex items-center gap-1">
                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span class="text-xs font-medium text-gray-700 dark:text-neutral-300">CREATE</span>
                    </div>
                    <span class="text-xs text-gray-500 dark:text-neutral-400">Crea</span>
                </div>
            </label>
            
            <label class="flex items-center p-2 bg-white dark:bg-neutral-900 rounded border border-gray-200 dark:border-neutral-700 hover:bg-gray-50 dark:hover:bg-neutral-800 cursor-pointer">
                <input type="checkbox" class="permission-checkbox shrink-0 border-gray-200 rounded text-yellow-600 focus:ring-yellow-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:checked:bg-yellow-500 dark:checked:border-yellow-500 dark:focus:ring-offset-gray-800" data-permission-type="edit">
                <div class="ms-2">
                    <div class="flex items-center gap-1">
                        <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        <span class="text-xs font-medium text-gray-700 dark:text-neutral-300">EDIT</span>
                    </div>
                    <span class="text-xs text-gray-500 dark:text-neutral-400">Modifica</span>
                </div>
            </label>
            
            <label class="flex items-center p-2 bg-white dark:bg-neutral-900 rounded border border-gray-200 dark:border-neutral-700 hover:bg-gray-50 dark:hover:bg-neutral-800 cursor-pointer">
                <input type="checkbox" class="permission-checkbox shrink-0 border-gray-200 rounded text-red-600 focus:ring-red-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:checked:bg-red-500 dark:checked:border-red-500 dark:focus:ring-offset-gray-800" data-permission-type="delete">
                <div class="ms-2">
                    <div class="flex items-center gap-1">
                        <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                        <span class="text-xs font-medium text-gray-700 dark:text-neutral-300">DELETE</span>
                    </div>
                    <span class="text-xs text-gray-500 dark:text-neutral-400">Elimina</span>
                </div>
            </label>
        </div>
    </div>
</template>

{% endblock %}

{% block pagescript %}
<!-- Form Plugins -->
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/select2.html" %}

<!-- Page Scripts -->
<script src="{{ contextPath }}/be/js/pages/user-permissions-manager.js?{{ buildNumber }}"></script>
{% endblock %}
